use crate::models::system_config::{
    CreateSystemConfigRequest, UpdateSystemConfigRequest, SystemConfigQuery,
    PlanAssignmentMode,
};
use crate::services::system_config_service::SystemConfigService;
use crate::utils::auth::get_user_from_token;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde_json::{json, Value};
use sqlx::PgPool;

// 获取所有系统配置
pub async fn get_system_configs(
    State(pool): State<PgPool>,
    Query(query): Query<SystemConfigQuery>,
    Extension(token): Extension<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    // 检查管理员权限
    let is_admin = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = $1 AND r.role_name = 'admin')",
        user.id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": "Database error"})),
        )
    })?
    .unwrap_or(false);

    if !is_admin {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let configs = service.get_all_configs(Some(query)).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "configs": configs,
        "total": configs.len()
    })))
}

// 获取按类别分组的配置
pub async fn get_configs_by_category(
    State(pool): State<PgPool>,
    Extension(token): Extension<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    // 检查管理员权限
    let is_admin = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = $1 AND r.role_name = 'admin')",
        user.id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": "Database error"})),
        )
    })?
    .unwrap_or(false);

    if !is_admin {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let grouped_configs = service.get_configs_by_category().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(grouped_configs)))
}

// 获取特定配置
pub async fn get_config_by_key(
    State(pool): State<PgPool>,
    Path(config_key): Path<String>,
    Extension(token): Extension<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let _user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    let service = SystemConfigService::new(pool);
    let config = service.get_typed_config(&config_key).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    match config {
        Some(config) => Ok(Json(json!(config))),
        None => Err((
            StatusCode::NOT_FOUND,
            Json(json!({"error": "Configuration not found"})),
        )),
    }
}

// 获取计划分配模式配置
pub async fn get_plan_assignment_mode(
    State(pool): State<PgPool>,
    Extension(token): Extension<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let _user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    let service = SystemConfigService::new(pool);
    let mode = service.get_plan_assignment_mode().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    let enabled = service.is_plan_assignment_mode_enabled().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "mode": mode,
        "enabled": enabled
    })))
}

// 创建新配置
pub async fn create_config(
    State(pool): State<PgPool>,
    Extension(token): Extension<String>,
    Json(request): Json<CreateSystemConfigRequest>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    // 检查管理员权限
    let is_admin = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = $1 AND r.role_name = 'admin')",
        user.id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": "Database error"})),
        )
    })?
    .unwrap_or(false);

    if !is_admin {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let config = service.create_config(request, user.id).await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(config)))
}

// 更新配置
pub async fn update_config(
    State(pool): State<PgPool>,
    Path(config_key): Path<String>,
    Extension(token): Extension<String>,
    Json(request): Json<UpdateSystemConfigRequest>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    // 检查管理员权限
    let is_admin = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = $1 AND r.role_name = 'admin')",
        user.id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": "Database error"})),
        )
    })?
    .unwrap_or(false);

    if !is_admin {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let service = SystemConfigService::new(pool);
    let config = service.update_config(&config_key, request, user.id).await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!(config)))
}

// 设置计划分配模式
pub async fn set_plan_assignment_mode(
    State(pool): State<PgPool>,
    Extension(token): Extension<String>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 验证用户权限
    let user = get_user_from_token(&pool, &token).await.map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(json!({"error": "Unauthorized"})),
        )
    })?;

    // 检查管理员权限
    let is_admin = sqlx::query_scalar!(
        "SELECT EXISTS(SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = $1 AND r.role_name = 'admin')",
        user.id
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": "Database error"})),
        )
    })?
    .unwrap_or(false);

    if !is_admin {
        return Err((
            StatusCode::FORBIDDEN,
            Json(json!({"error": "Admin access required"})),
        ));
    }

    let mode_str = payload["mode"].as_str().ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": "Missing or invalid mode field"})),
        )
    })?;

    let mode = PlanAssignmentMode::from_string(mode_str).map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({"error": e})),
        )
    })?;

    let service = SystemConfigService::new(pool);
    let config = service.set_plan_assignment_mode(mode, user.id).await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({"error": e})),
        )
    })?;

    Ok(Json(json!({
        "message": "Plan assignment mode updated successfully",
        "config": config
    })))
}
